import { Request, Response } from 'express';
import {
  getCurrentUserReferees,
  getRefereeDetails,
  getRefereeDepartment,
  getRefereeManager,
  getRefereeTeamMembers,
  getRefereeProjects,
  getRefereePermissions,
  getComprehensiveRefereeInfo,
  importRefereeUsers,
  ImportableReferee,
  getRefereeTransactionHistory,
  addRefereeBank,
  getRefereeBank,
  getRefereeBanks,
  transferAmountToReferee
} from '../services/refereeService';
import { executeQuery } from '../utils/database';
import { sendSuccess, sendError, sendUnauthorized, sendUserSuccess, sendUserError } from '../utils/response';
import logger from '../utils/logger';

/**
 * Get current user's referee members
 */
export const getUserReferees = async (req: Request & { userId?: string; team_connect_user_id?: string }, res: Response) => {
  try {
    const userId = req.userId;
    const team_connect_user_id = req.team_connect_user_id;
    if (!userId || !team_connect_user_id) {
      return sendUnauthorized(res, 'Authentication required');
    }
    console.log(userId, team_connect_user_id, req);

    const referees = await getCurrentUserReferees(parseInt(userId), parseInt(team_connect_user_id));
    sendSuccess(res, referees, 'Referee members retrieved successfully');
  } catch (error) {
    logger.error('Error getting user referees', {
      error: error instanceof Error ? error.message : 'Unknown error',
      userId: req.userId,
      team_connect_user_id: req.team_connect_user_id
    });
    sendError(res, 'Failed to retrieve referee members', 500);
  }
};

/**
 * Get referee details from secondary database
 */
export const getRefereeDetailsInfo = async (req: Request & { userId?: string; team?: string }, res: Response) => {
  try {
   const userId = req.query.id as string;
   

    if (!userId) {
      return sendUnauthorized(res, 'Authentication required');
    }

    const refereeDetails = await getRefereeDetails(parseInt(userId));

    if (!refereeDetails) {
      return sendError(res, 'Referee details not found', 404);
    }

    sendSuccess(res, refereeDetails, 'Referee details retrieved successfully');
  } catch (error) {
    logger.error('Error getting referee details', {
      error: error instanceof Error ? error.message : 'Unknown error',
      userId: req.userId,
      parentUserId: req.master_parent_user_id
    });
    sendError(res, 'Failed to retrieve referee details', 500);
  }
};

/**
 * Get referee's department information
 */
export const getRefereeDepartmentInfo = async (req: Request & { userId?: string; parent_user_id?: string }, res: Response) => {
  try {
    const userId = req.userId;
    const parentUserId = req.parent_user_id;

    if (!userId) {
      return sendUnauthorized(res, 'Authentication required');
    }

    const department = await getRefereeDepartment(parseInt(userId));
    sendSuccess(res, department, 'Department information retrieved successfully');
  } catch (error) {
    logger.error('Error getting referee department', {
      error: error instanceof Error ? error.message : 'Unknown error',
      userId: req.userId,
      parentUserId: req.parent_user_id
    });
    sendError(res, 'Failed to retrieve department information', 500);
  }
};

/**
 * Get referee's manager information
 */
export const getRefereeManagerInfo = async (req: Request & { userId?: string; parent_user_id?: string }, res: Response) => {
  try {
    const userId = req.userId;
    const parentUserId = req.parent_user_id;

    if (!userId) {
      return sendUnauthorized(res, 'Authentication required');
    }

    const manager = await getRefereeManager(parseInt(userId));
    sendSuccess(res, manager, 'Manager information retrieved successfully');
  } catch (error) {
    logger.error('Error getting referee manager', {
      error: error instanceof Error ? error.message : 'Unknown error',
      userId: req.userId,
      parentUserId: req.parent_user_id
    });
    sendError(res, 'Failed to retrieve manager information', 500);
  }
};

/**
 * Get referee's team members
 */
export const getRefereeTeam = async (req: Request & { userId?: string } & { team_connect_user_id?: string }, res: Response) => {
  try {
    const userId = req.team_connect_user_id;
    
    if (!userId) {
      return sendUnauthorized(res, 'Authentication required');
    }

    const teamMembers = await getRefereeTeamMembers(parseInt(userId));
    sendSuccess(res, teamMembers, 'Team members retrieved successfully');
  } catch (error) {
    logger.error('Error getting referee team', {
      error: error instanceof Error ? error.message : 'Unknown error',
      userId: req.userId
    });
    sendError(res, 'Failed to retrieve team members', 500);
  }
};

/**
 * Get referee's projects
 */
export const getRefereeProjectsInfo = async (req: Request & { userId?: string }, res: Response) => {
  try {
    const userId = req.userId;

    if (!userId) {
      return sendUnauthorized(res, 'Authentication required');
    }

    const projects = await getRefereeProjects(parseInt(userId));
    sendSuccess(res, projects, 'Projects retrieved successfully');
  } catch (error) {
    logger.error('Error getting referee projects', {
      error: error instanceof Error ? error.message : 'Unknown error',
      userId: req.userId
    });
    sendError(res, 'Failed to retrieve projects', 500);
  }
};

/**
 * Get referee's permissions
 */
export const getRefereePermissionsInfo = async (req: Request & { userId?: string }, res: Response) => {
  try {
    const userId = req.userId;

    if (!userId) {
      return sendUnauthorized(res, 'Authentication required');
    }

    const permissions = await getRefereePermissions(parseInt(userId));
    sendSuccess(res, permissions, 'Permissions retrieved successfully');
  } catch (error) {
    logger.error('Error getting referee permissions', {
      error: error instanceof Error ? error.message : 'Unknown error',
      userId: req.userId
    });
    sendError(res, 'Failed to retrieve permissions', 500);
  }
};

/**
 * Get comprehensive referee information
 */
export const getComprehensiveRefereeData = async (req: Request & { userId?: string; parent_user_id?: string }, res: Response) => {
  try {
    const userId = req.userId;
    const parentUserId = req.parent_user_id;

    if (!userId) {
      return sendUnauthorized(res, 'Authentication required');
    }

    const comprehensiveInfo = await getComprehensiveRefereeInfo(
      parseInt(userId),
      parentUserId ? parseInt(parentUserId) : undefined
    );
    sendSuccess(res, comprehensiveInfo, 'Comprehensive referee information retrieved successfully');
  } catch (error) {
    logger.error('Error getting comprehensive referee info', {
      error: error instanceof Error ? error.message : 'Unknown error',
      userId: req.userId,
      parentUserId: req.parent_user_id
    });
    sendError(res, 'Failed to retrieve comprehensive referee information', 500);
  }
};

/**
 * Import multiple users as referee members
 */
export const importReferees = async (req: Request & { userId?: string; team_connect_user_id?: string }, res: Response) => {
  try {
    const userId = req.userId;
    const team_connect_user_id = req.team_connect_user_id;
    const { users, defaultRole } = req.body;

    if (!userId || !team_connect_user_id) {
      return sendUnauthorized(res, 'Authentication required');
    }

    // Validate request body
    if (!users || !Array.isArray(users) || users.length === 0) {
      return sendError(res, 'Users array is required and cannot be empty', 400);
    }

    // Validate each user object
    for (const user of users) {
      if (!user.id || !user.name || !user.email) {
        return sendError(res, 'Each user must have id, name, and email', 400);
      }
    }

    // Validate default role
    const validRoles = ['Staff', 'Referee'];
    if (defaultRole && !validRoles.includes(defaultRole)) {
      return sendError(res, 'Invalid default role. Must be one of: Staff, Referee', 400);
    }
    console.log(users, defaultRole);

    // Import users
    const result = await importRefereeUsers(
      users as ImportableReferee[],
      parseInt(team_connect_user_id),
      defaultRole
    );

    if (result.success) {
      logger.info('Referee import completed successfully', {
        userId,
        team_connect_user_id,
        imported: result.imported,
        failed: result.failed
      });

      sendSuccess(res, {
        imported: result.imported,
        failed: result.failed,
        message: result.message,
        errors: result.errors
      }, 'Referee import completed');
    } else {
      logger.warn('Referee import completed with errors', {
        userId,
        team_connect_user_id,
        imported: result.imported,
        failed: result.failed,
        errors: result.errors
      });

      sendError(res, result.message, 400, result.errors?.join('; '));
    }

  } catch (error) {
    logger.error('Error importing referee users', {
      error: error instanceof Error ? error.message : 'Unknown error',
      userId: req.userId,
      parentUserId: req.master_parent_user_id
    });
    sendError(res, 'Failed to import referee users', 500);
  }
};

/**
 * Get referee member's transaction history with pagination and filtering
 */
export const getRefereeTransactions = async (req: Request, res: Response) => {
  try {
    const userId = req.query.userId as string;
    const {
      limit = '20',
      offset = '0',
      search = '',
      type = '',
      status = '',
      dateFrom = '',
      dateTo = ''
    } = req.query;

    logger.info('Referee transactions request received', {
      userId,
      userIdType: typeof userId,
      allQueryParams: req.query,
      limit,
      offset,
      search,
      type,
      status,
      dateFrom,
      dateTo
    });

    if (!userId) {
      logger.error('Missing userId parameter');
      return sendError(res, 'userId parameter required', 400);
    }

    if (userId === 'undefined' || userId === 'null') {
      logger.error('Invalid userId parameter', { userId });
      return sendError(res, 'Invalid userId parameter', 400);
    }

    const result = await getRefereeTransactionHistory(parseInt(userId), {
      limit: parseInt(limit as string),
      offset: parseInt(offset as string),
      search: search as string,
      type: type as string,
      status: status as string,
      dateFrom: dateFrom as string,
      dateTo: dateTo as string
    });

    sendSuccess(res, {
      transactions: result.transactions,
      pagination: {
        total: result.total,
        limit: parseInt(limit as string),
        offset: parseInt(offset as string),
        hasMore: result.hasMore,
        currentPage: Math.floor(parseInt(offset as string) / parseInt(limit as string)) + 1,
        totalPages: Math.ceil(result.total / parseInt(limit as string))
      },
      filters: {
        search: search as string,
        type: type as string,
        status: status as string,
        dateFrom: dateFrom as string,
        dateTo: dateTo as string
      }
    }, 'Referee transactions retrieved successfully');

  } catch (error) {
    logger.error('Error fetching referee transactions', {
      error: error instanceof Error ? error.message : 'Unknown error',
      userId: req.query.userId
    });
    sendError(res, 'Failed to retrieve referee transactions', 500);
  }
};

// Add referee bank account
export const addRefereeBankAccount = async (req: Request & { userId?: string; team_connect_user_id?: string }, res: Response) => {
  try {
    const team_connect_user_id = req.team_connect_user_id;
    const { staffId, bankAccountData } = req.body; // Keep staffId for compatibility

    if (!staffId || !bankAccountData) {
      return sendError(res, 'Referee ID and bank account data are required', 400);
    }

    if (!team_connect_user_id) {
      return sendError(res, 'Team connect user ID is required', 400);
    }

    // Add the bank account
    const result = await addRefereeBank(
      staffId, // Use staffId as refereeId for compatibility
      bankAccountData,
      parseInt(team_connect_user_id)
    );

    if (result.success) {
      sendUserSuccess(res, 'BANK', 'ADDED', result);
    } else {
      sendUserError(res, 'BANK', 'ADDED', 400, result.message);
    }
  } catch (error) {
    logger.error('Error in addRefereeBankAccount controller', { error });
    sendError(res, 'Failed to add referee bank account', 500);
  }
};

// Transfer to referee bank
export const transferToRefereeBank = async (req: Request & { userId?: string; team_connect_user_id?: string }, res: Response) => {
  try {
    const team_connect_user_id  = req.team_connect_user_id;
    const {
      staffId, // Keep staffId for compatibility (represents refereeId)
      amount,
      description,
      pin,
      paymentSource = 'wallet',
      bankAccountId,
      staffBankAccountId, // Keep for compatibility (represents refereeBankAccountId)
      paymentMethodType = 'ach'  // New parameter for payment method type
    } = req.body;

    if (!staffId || !amount) {
      return sendError(res, 'Referee ID and amount are required', 400);
    }

    if (amount < 1 || amount > 10000) {
      return sendError(res, 'Amount must be between $1 and $10,000', 400);
    }

    if (paymentSource === 'wallet' && !pin) {
      return sendError(res, 'PIN is required for wallet transfers', 400);
    }

    if (paymentSource === 'bank' && !bankAccountId) {
      return sendError(res, 'Bank account ID is required for bank transfers', 400);
    }

    if (!staffBankAccountId) {
      return sendError(res, 'Referee bank account ID is required', 400);
    }

    if(!team_connect_user_id) {
      return sendError(res, 'Team connect user ID is required', 400);
    }

    if(!req.userId) {
      return sendError(res, 'User ID is required', 400);
    }

    // Import the createStaffBankTransfer function from plaidTransferService (reuse for referees)
    const { createStaffBankTransfer } = await import('../services/plaidTransferService');

    // Create real-time transfer using Plaid (includes verification and transfer)
    const transferResult = await createStaffBankTransfer(
      parseInt(team_connect_user_id),
      parseInt(staffId), // refereeId
      amount,
      description || 'Referee payment',
      staffBankAccountId, // refereeBankAccountId
      paymentMethodType,
      pin, // Pass PIN for wallet verification
      paymentSource,
      bankAccountId
    );

    if (transferResult.success) {
      sendUserSuccess(res, 'TRANSACTIONS', 'COMPLETED', {
        success: true,
        message: transferResult.message,
        transferId: transferResult.transferId,
        status: transferResult.status
      });
    } else {
      sendUserError(res, 'TRANSACTIONS', 'FAILED', 400, transferResult.message);
    }
  } catch (error) {
    logger.error('Error in transferToRefereeBank controller', { error });
    sendError(res, 'Failed to transfer funds', 500);
  }
};

// Get referee bank accounts
export const getRefereeBankAccounts = async (req: Request, res: Response) => {
  try {
    const team_connect_user_id = req.team_connect_user_id;

    if (!team_connect_user_id) {
      return sendError(res, 'Team connect user ID is required', 400);
    }

    const accounts = await getRefereeBank(parseInt(team_connect_user_id));

    sendUserSuccess(res, 'BANK', 'FETCHED', { accounts });
  } catch (error) {
    logger.error('Error in getRefereeBankAccounts controller', { error });
    sendError(res, 'Failed to fetch referee bank accounts', 500);
  }
};

export const getSingleRefereeBankAccounts = async(req: Request, res: Response) => {
  try {
    const { staffId } = req.params; // Keep staffId for compatibility (represents refereeId)
    const team_connect_user_id = req.team_connect_user_id;

    if (!team_connect_user_id) {
      return sendError(res, 'Team connect user ID is required', 400);
    }

    const accounts = await getRefereeBanks(parseInt(team_connect_user_id), parseInt(staffId));

    sendUserSuccess(res, 'BANK', 'FETCHED', { accounts });
  } catch (error) {
    logger.error('Error in getSingleRefereeBankAccounts controller', { error });
    sendError(res, 'Failed to fetch referee bank accounts', 500);
  }
};

// Update referee bank account
export const updateRefereeBankAccount = async (req: Request & { userId?: string; team_connect_user_id?: string }, res: Response) => {
  try {
    const { accountId } = req.params;
    const team_connect_user_id = req.team_connect_user_id;
    const { bankAccountData } = req.body;

    if (!accountId || !bankAccountData) {
      return sendError(res, 'Account ID and bank account data are required', 400);
    }

    if (!team_connect_user_id) {
      return sendError(res, 'Team connect user ID is required', 400);
    }

    // Import the update function
    const { updateRefereeBank } = await import('../services/refereeService');

    const result = await updateRefereeBank(
      parseInt(accountId),
      bankAccountData,
      parseInt(team_connect_user_id)
    );

    if (result.success) {
      sendUserSuccess(res, 'BANK', 'UPDATED', result);
    } else {
      sendUserError(res, 'BANK', 'UPDATED', 400, result.message);
    }
  } catch (error) {
    logger.error('Error in updateRefereeBankAccount controller', { error });
    sendError(res, 'Failed to update bank account', 500);
  }
};

/**
 * Sync transfer statuses with Plaid for referee transfers
 */
export const syncRefereeTransferStatus = async (req: Request, res: Response) => {
  try {
    const userId = req.userId;
    if (!userId) {
      return sendUnauthorized(res, 'Authentication required');
    }

    logger.info('Starting referee transfer status sync', { userId });

    // Import the sync function from plaidTransferService (reuse for referees)
    const { syncPendingTransfers } = await import('../services/plaidTransferService');

    // Sync all pending transfers (includes referee transfers)
    await syncPendingTransfers();

    logger.info('Referee transfer status sync completed', { userId });
    sendSuccess(res, { message: 'Referee transfer statuses synced successfully' }, 'Referee transfer statuses synced successfully');
  } catch (error) {
    logger.error('Error syncing referee transfer statuses', {
      error: error instanceof Error ? error.message : 'Unknown error',
      userId: req.userId
    });
    sendError(res, 'Failed to sync referee transfer statuses', 500);
  }
};

/**
 * Get referee transfer status by transfer ID
 */
export const getRefereeTransferStatus = async (req: Request, res: Response) => {
  try {
    const { transferId } = req.params;
    const userId = req.userId;

    if (!userId) {
      return sendUnauthorized(res, 'Authentication required');
    }

    if (!transferId) {
      return sendError(res, 'Transfer ID is required', 400);
    }

    logger.info('Getting referee transfer status', { userId, transferId });

    // Import the transfer status function from plaidTransferService (reuse for referees)
    const { getTransferStatus: getPlaidTransferStatus } = await import('../services/plaidTransferService');

    // Get the transfer status from Plaid
    const statusResult = await getPlaidTransferStatus(transferId);

    if (statusResult.success) {
      sendSuccess(res, statusResult, 'Referee transfer status retrieved successfully');
    } else {
      sendError(res, statusResult.message || 'Failed to get referee transfer status', 400);
    }
  } catch (error) {
    logger.error('Error getting referee transfer status', {
      error: error instanceof Error ? error.message : 'Unknown error',
      userId: req.userId,
      transferId: req.params.transferId
    });
    sendError(res, 'Failed to get referee transfer status', 500);
  }
};
