import { Request, Response } from 'express';
import logger from '../utils/logger';
import { sendSuccess, sendError } from '../utils/response';
import {
  simulateTransferEvent as simulateTransferEventService,
  getPendingTransfers,
  simulateMultipleTransfers,
  isSimulationAvailable,
  getSimulationInfo,
  TransferSimulationRequest
} from '../services/plaidTransferSimulationService';

/**
 * Simulate transfer success (posted)
 */
export const simulateTransferSuccess = async (req: Request, res: Response) => {
  try {
    if (!isSimulationAvailable()) {
      return sendError(res, 'Transfer simulation is only available in sandbox environment', 403);
    }

    const { transferId } = req.params;
    
    if (!transferId) {
      return sendError(res, 'Transfer ID is required', 400);
    }

    logger.info('Simulating transfer success', { transferId });

    const result = await simulateTransferEventService(transferId, 'posted');

    if (result.success) {
      return sendSuccess(res, {
        transferId: result.transferId,
        eventType: result.eventType,
        status: 'success'
      }, result.message);
    } else {
      return sendError(res, result.message, 400, result.error);
    }

  } catch (error) {
    logger.error('Error in simulateTransferSuccess', {
      error: error instanceof Error ? error.message : 'Unknown error',
      transferId: req.params.transferId
    });
    return sendError(res, 'Failed to simulate transfer success', 500);
  }
};

/**
 * Simulate transfer failure
 */
export const simulateTransferFailure = async (req: Request, res: Response) => {
  try {
    if (!isSimulationAvailable()) {
      return sendError(res, 'Transfer simulation is only available in sandbox environment', 403);
    }

    const { transferId } = req.params;
    const { failureReason } = req.body;
    
    if (!transferId) {
      return sendError(res, 'Transfer ID is required', 400);
    }

    logger.info('Simulating transfer failure', { 
      transferId, 
      failureReason: failureReason || 'insufficient_funds' 
    });

    const result = await simulateTransferEventService(
      transferId,
      'failed',
      failureReason || 'insufficient_funds'
    );

    if (result.success) {
      return sendSuccess(res, {
        transferId: result.transferId,
        eventType: result.eventType,
        status: 'failed',
        failureReason: failureReason || 'insufficient_funds'
      }, result.message);
    } else {
      return sendError(res, result.message, 400, result.error);
    }

  } catch (error) {
    logger.error('Error in simulateTransferFailure', {
      error: error instanceof Error ? error.message : 'Unknown error',
      transferId: req.params.transferId
    });
    return sendError(res, 'Failed to simulate transfer failure', 500);
  }
};

/**
 * Simulate transfer event (generic endpoint)
 */
export const simulateTransferEvent = async (req: Request, res: Response) => {
  try {
    if (!isSimulationAvailable()) {
      return sendError(res, 'Transfer simulation is only available in sandbox environment', 403);
    }

    const { transferId } = req.params;
    const { eventType, failureReason } = req.body;
    
    if (!transferId) {
      return sendError(res, 'Transfer ID is required', 400);
    }

    if (!eventType || !['posted', 'failed'].includes(eventType)) {
      return sendError(res, 'Valid event type (posted or failed) is required', 400);
    }

    logger.info('Simulating transfer event', { 
      transferId, 
      eventType,
      failureReason 
    });

    const result = await simulateTransferEventService(transferId, eventType, failureReason);

    if (result.success) {
      return sendSuccess(res, {
        transferId: result.transferId,
        eventType: result.eventType,
        status: eventType,
        ...(failureReason && { failureReason })
      }, result.message);
    } else {
      return sendError(res, result.message, 400, result.error);
    }

  } catch (error) {
    logger.error('Error in simulateTransferEvent', {
      error: error instanceof Error ? error.message : 'Unknown error',
      transferId: req.params.transferId,
      eventType: req.body.eventType
    });
    return sendError(res, 'Failed to simulate transfer event', 500);
  }
};

/**
 * Get list of pending transfers that can be simulated
 */
export const getPendingTransfersController = async (req: Request, res: Response) => {
  try {
    if (!isSimulationAvailable()) {
      return sendError(res, 'Transfer simulation is only available in sandbox environment', 403);
    }

    logger.info('Getting pending transfers for simulation');

    const transfers = await getPendingTransfers();

    return sendSuccess(res, {
      transfers,
      count: transfers.length,
      environment: 'sandbox'
    }, 'Pending transfers retrieved successfully');

  } catch (error) {
    logger.error('Error in getPendingTransfersController', {
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    return sendError(res, 'Failed to get pending transfers', 500);
  }
};

/**
 * Simulate multiple transfers at once
 */
export const simulateMultipleTransfersController = async (req: Request, res: Response) => {
  try {
    if (!isSimulationAvailable()) {
      return sendError(res, 'Transfer simulation is only available in sandbox environment', 403);
    }

    const { transfers } = req.body;
    
    if (!transfers || !Array.isArray(transfers) || transfers.length === 0) {
      return sendError(res, 'Array of transfer simulation requests is required', 400);
    }

    // Validate each transfer request
    for (const transfer of transfers) {
      if (!transfer.transferId || !transfer.eventType) {
        return sendError(res, 'Each transfer must have transferId and eventType', 400);
      }
      if (!['posted', 'failed'].includes(transfer.eventType)) {
        return sendError(res, 'Event type must be either "posted" or "failed"', 400);
      }
    }

    logger.info('Simulating multiple transfers', { 
      count: transfers.length,
      transfers: transfers.map(t => ({ id: t.transferId, type: t.eventType }))
    });

    const results = await simulateMultipleTransfers(transfers as TransferSimulationRequest[]);

    const successCount = results.filter(r => r.success).length;
    const failureCount = results.filter(r => !r.success).length;

    return sendSuccess(res, {
      results,
      summary: {
        total: results.length,
        successful: successCount,
        failed: failureCount
      }
    }, `Simulated ${successCount} transfers successfully, ${failureCount} failed`);

  } catch (error) {
    logger.error('Error in simulateMultipleTransfersController', {
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    return sendError(res, 'Failed to simulate multiple transfers', 500);
  }
};

/**
 * Get simulation environment info
 */
export const getSimulationInfoController = async (req: Request, res: Response) => {
  try {
    const info = getSimulationInfo();
    
    return sendSuccess(res, info, 'Simulation info retrieved successfully');

  } catch (error) {
    logger.error('Error in getSimulationInfoController', {
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    return sendError(res, 'Failed to get simulation info', 500);
  }
};
